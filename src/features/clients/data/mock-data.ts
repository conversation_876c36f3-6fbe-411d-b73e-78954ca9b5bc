import { Client } from './schema'

export const mockClients: Client[] = [
  {
    id: 1,
    name: 'Acme Corporation',
    code: 'ACME',
    description: 'Leading technology solutions provider specializing in enterprise software development and digital transformation.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '123 Business Ave, Tech City, TC 12345',
    isActive: true,
    createdAt: '2024-01-15T08:00:00Z',
    updatedAt: '2024-01-15T08:00:00Z',
  },
  {
    id: 2,
    name: 'TechStart Inc.',
    code: 'TECH',
    description: 'Innovative startup focused on AI and machine learning solutions for modern businesses.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '456 Innovation Blvd, Startup Valley, SV 67890',
    isActive: true,
    createdAt: '2024-02-01T09:30:00Z',
    updatedAt: '2024-02-01T09:30:00Z',
  },
  {
    id: 3,
    name: 'Global Solutions Ltd.',
    code: 'GLOB',
    description: 'International consulting firm providing strategic business solutions across multiple industries.',
    contactEmail: '<EMAIL>',
    contactPhone: '+44 20 7123 4567',
    address: '789 Global Plaza, London, UK EC1A 1BB',
    isActive: true,
    createdAt: '2024-01-20T10:15:00Z',
    updatedAt: '2024-03-10T14:22:00Z',
  },
  {
    id: 4,
    name: 'Innovation Labs',
    code: 'INNO',
    description: 'Research and development company focused on cutting-edge technology innovations.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '321 Research Park, Innovation City, IC 54321',
    isActive: true,
    createdAt: '2024-02-15T11:45:00Z',
    updatedAt: '2024-02-15T11:45:00Z',
  },
  {
    id: 5,
    name: 'Digital Dynamics',
    code: 'DIGI',
    description: 'Digital marketing and web development agency helping businesses establish their online presence.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '654 Digital Way, Web City, WC 98765',
    isActive: true,
    createdAt: '2024-03-01T13:20:00Z',
    updatedAt: '2024-03-01T13:20:00Z',
  },
  {
    id: 6,
    name: 'Enterprise Systems Corp',
    code: 'ESYS',
    description: 'Large-scale enterprise software solutions and system integration services.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '987 Enterprise Dr, Corporate City, CC 13579',
    isActive: true,
    createdAt: '2024-01-10T07:30:00Z',
    updatedAt: '2024-01-10T07:30:00Z',
  },
  {
    id: 7,
    name: 'CloudFirst Technologies',
    code: 'CLOUD',
    description: 'Cloud infrastructure and migration services for modern businesses.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '147 Cloud Street, Sky City, SC 24680',
    isActive: true,
    createdAt: '2024-02-20T16:00:00Z',
    updatedAt: '2024-02-20T16:00:00Z',
  },
  {
    id: 8,
    name: 'Legacy Systems Inc.',
    code: 'LEGACY',
    description: 'Maintenance and modernization of legacy enterprise systems.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '258 Legacy Lane, Old Town, OT 97531',
    isActive: false,
    createdAt: '2023-12-01T12:00:00Z',
    updatedAt: '2024-03-15T09:45:00Z',
  },
  {
    id: 9,
    name: 'Mobile First Solutions',
    code: 'MOBILE',
    description: 'Mobile app development and responsive web design specialists.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '369 Mobile Ave, App City, AC 86420',
    isActive: true,
    createdAt: '2024-03-05T14:30:00Z',
    updatedAt: '2024-03-05T14:30:00Z',
  },
  {
    id: 10,
    name: 'Data Analytics Pro',
    code: 'DATA',
    description: 'Advanced data analytics and business intelligence solutions.',
    contactEmail: '<EMAIL>',
    contactPhone: '+****************',
    address: '741 Analytics Blvd, Data City, DC 75319',
    isActive: true,
    createdAt: '2024-02-28T10:00:00Z',
    updatedAt: '2024-02-28T10:00:00Z',
  },
]
