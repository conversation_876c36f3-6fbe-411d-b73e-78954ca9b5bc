import { toast } from '@/hooks/use-toast'
import { ConfirmDialog } from '@/components/confirm-dialog'
import { useClients } from '../context/clients-context'
import { ClientMutateDrawer } from './client-mutate-drawer'
import { ClientViewDialog } from './client-view-dialog'

export function ClientsDialogs() {
  const { open, setOpen, currentRow, setCurrentRow } = useClients()

  const handleDelete = () => {
    if (!currentRow) return

    // TODO: Implement actual delete API call
    console.log('Deleting client:', currentRow.id)

    toast({
      title: 'Client deleted',
      description: `Successfully deleted ${currentRow.name}.`,
    })

    setCurrentRow(null)
    setOpen(null)
  }

  return (
    <>
      <ClientMutateDrawer
        key='client-create'
        open={open === 'create'}
        onOpenChange={(isOpen) => {
          if (!isOpen) {
            setOpen(null)
          }
        }}
      />

      {currentRow && (
        <>
          <ClientMutateDrawer
            key={`client-edit-${currentRow.id}`}
            open={open === 'edit'}
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            currentRow={currentRow}
          />

          <ClientViewDialog
            key={`client-view-${currentRow.id}`}
            open={open === 'view'}
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            client={currentRow}
          />

          <ConfirmDialog
            key={`client-delete-${currentRow.id}`}
            open={open === 'delete'}
            onOpenChange={(isOpen) => {
              if (!isOpen) {
                setOpen(null)
                setTimeout(() => {
                  setCurrentRow(null)
                }, 500)
              }
            }}
            handleConfirm={handleDelete}
            title={`Delete ${currentRow.name}`}
            desc={`This action cannot be undone. This will permanently delete the client "${currentRow.name}" and remove all associated data.`}
          />
        </>
      )}
    </>
  )
}
