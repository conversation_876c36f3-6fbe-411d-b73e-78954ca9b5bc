import { IconBuilding, IconMail, IconPhone, IconMapPin, IconCalendar } from '@tabler/icons-react'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Separator } from '@/components/ui/separator'
import { Client } from '../data/schema'

interface ClientViewDialogProps {
  open: boolean
  onOpenChange: () => void
  client: Client
}

export function ClientViewDialog({
  open,
  onOpenChange,
  client,
}: ClientViewDialogProps) {
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='max-w-2xl'>
        <DialogHeader>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10'>
              <IconBuilding className='h-5 w-5 text-primary' />
            </div>
            <div>
              <DialogTitle className='text-xl'>{client.name}</DialogTitle>
              <DialogDescription>
                Client details and information
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Basic Information */}
          <div className='space-y-4'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-semibold'>Basic Information</h3>
              <Badge 
                variant={client.isActive ? 'default' : 'secondary'}
                className={
                  client.isActive 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' 
                    : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'
                }
              >
                {client.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
            
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div>
                <label className='text-sm font-medium text-muted-foreground'>
                  Client Name
                </label>
                <p className='mt-1 text-sm'>{client.name}</p>
              </div>
              <div>
                <label className='text-sm font-medium text-muted-foreground'>
                  Client Code
                </label>
                <p className='mt-1'>
                  <Badge variant='secondary' className='font-mono text-xs'>
                    {client.code}
                  </Badge>
                </p>
              </div>
            </div>

            {client.description && (
              <div>
                <label className='text-sm font-medium text-muted-foreground'>
                  Description
                </label>
                <p className='mt-1 text-sm text-muted-foreground'>
                  {client.description}
                </p>
              </div>
            )}
          </div>

          <Separator />

          {/* Contact Information */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold'>Contact Information</h3>
            
            <div className='space-y-3'>
              {client.contactEmail ? (
                <div className='flex items-center gap-3'>
                  <IconMail className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Email
                    </label>
                    <p className='text-sm'>{client.contactEmail}</p>
                  </div>
                </div>
              ) : (
                <div className='flex items-center gap-3'>
                  <IconMail className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Email
                    </label>
                    <p className='text-sm text-muted-foreground italic'>No email provided</p>
                  </div>
                </div>
              )}

              {client.contactPhone ? (
                <div className='flex items-center gap-3'>
                  <IconPhone className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Phone
                    </label>
                    <p className='text-sm'>{client.contactPhone}</p>
                  </div>
                </div>
              ) : (
                <div className='flex items-center gap-3'>
                  <IconPhone className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Phone
                    </label>
                    <p className='text-sm text-muted-foreground italic'>No phone provided</p>
                  </div>
                </div>
              )}

              {client.address ? (
                <div className='flex items-start gap-3'>
                  <IconMapPin className='h-4 w-4 text-muted-foreground mt-0.5' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Address
                    </label>
                    <p className='text-sm whitespace-pre-line'>{client.address}</p>
                  </div>
                </div>
              ) : (
                <div className='flex items-center gap-3'>
                  <IconMapPin className='h-4 w-4 text-muted-foreground' />
                  <div>
                    <label className='text-sm font-medium text-muted-foreground'>
                      Address
                    </label>
                    <p className='text-sm text-muted-foreground italic'>No address provided</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          <Separator />

          {/* Timestamps */}
          <div className='space-y-4'>
            <h3 className='text-lg font-semibold'>Timeline</h3>
            
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='flex items-center gap-3'>
                <IconCalendar className='h-4 w-4 text-muted-foreground' />
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>
                    Created
                  </label>
                  <p className='text-sm'>{formatDate(client.createdAt)}</p>
                </div>
              </div>
              
              <div className='flex items-center gap-3'>
                <IconCalendar className='h-4 w-4 text-muted-foreground' />
                <div>
                  <label className='text-sm font-medium text-muted-foreground'>
                    Last Updated
                  </label>
                  <p className='text-sm'>{formatDate(client.updatedAt)}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
