import { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { IconBuilding } from '@tabler/icons-react'

import { Button } from '@/components/ui/button'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { toast } from '@/hooks/use-toast'

import { Client, ClientForm, clientFormSchema } from '../data/schema'

interface ClientMutateDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  currentRow?: Client
}

export function ClientMutateDrawer({
  open,
  onOpenChange,
  currentRow,
}: ClientMutateDialogProps) {
  const isEdit = !!currentRow
  const title = isEdit ? 'Edit Client' : 'Add New Client'
  const description = isEdit
    ? 'Update the client information below.'
    : 'Fill in the information below to add a new client.'

  const form = useForm<ClientForm>({
    resolver: zodResolver(clientFormSchema),
    defaultValues: {
      name: '',
      code: '',
      description: '',
      contactEmail: '',
      contactPhone: '',
      address: '',
      isActive: true,
    },
  })

  // Reset form when opening/closing or changing currentRow
  useEffect(() => {
    if (open && currentRow) {
      form.reset({
        name: currentRow.name,
        code: currentRow.code,
        description: currentRow.description || '',
        contactEmail: currentRow.contactEmail || '',
        contactPhone: currentRow.contactPhone || '',
        address: currentRow.address || '',
        isActive: currentRow.isActive,
      })
    } else if (open && !currentRow) {
      form.reset({
        name: '',
        code: '',
        description: '',
        contactEmail: '',
        contactPhone: '',
        address: '',
        isActive: true,
      })
    }
  }, [open, currentRow, form])

  const onSubmit = (data: ClientForm) => {
    // TODO: Implement actual API call
    console.log(isEdit ? 'Updating client:' : 'Creating client:', data)

    toast({
      title: isEdit ? 'Client updated' : 'Client created',
      description: isEdit
        ? `Successfully updated ${data.name}.`
        : `Successfully created ${data.name}.`,
    })

    form.reset()
    onOpenChange(false)
  }

  return (
    <Dialog
      open={open}
      onOpenChange={(state) => {
        form.reset()
        onOpenChange(state)
      }}
    >
      <DialogContent className='sm:max-w-2xl'>
        <DialogHeader className='text-left'>
          <div className='flex items-center gap-3'>
            <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10'>
              <IconBuilding className='h-5 w-5 text-primary' />
            </div>
            <div>
              <DialogTitle>{title}</DialogTitle>
              <DialogDescription>{description}</DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className='-mr-4 h-[26.25rem] w-full overflow-y-auto py-1 pr-4'>
          <Form {...form}>
            <form
              id='client-form'
              onSubmit={form.handleSubmit(onSubmit)}
              className='space-y-4 p-0.5'
            >
              <FormField
                control={form.control}
                name='name'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Client Name *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='Enter client name'
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='code'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Client Code *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='e.g., ACME'
                        className='col-span-4'
                        {...field}
                        onChange={(e) => field.onChange(e.target.value.toUpperCase())}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='description'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Description
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Enter client description'
                        className='col-span-4 min-h-[80px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='contactEmail'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Contact Email
                    </FormLabel>
                    <FormControl>
                      <Input
                        type='email'
                        placeholder='<EMAIL>'
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='contactPhone'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Contact Phone
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder='+****************'
                        className='col-span-4'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='address'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Address
                    </FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder='Enter client address'
                        className='col-span-4 min-h-[60px]'
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name='isActive'
                render={({ field }) => (
                  <FormItem className='grid grid-cols-6 items-center gap-x-4 gap-y-1 space-y-0'>
                    <FormLabel className='col-span-2 text-right'>
                      Active Status
                    </FormLabel>
                    <div className='col-span-4 flex items-center justify-between rounded-lg border p-3'>
                      <div className='space-y-0.5'>
                        <div className='text-sm text-muted-foreground'>
                          Enable this client for new projects and time tracking
                        </div>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className='col-span-4 col-start-3' />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
        <DialogFooter>
          <Button type='submit' form='client-form'>
            {isEdit ? 'Update Client' : 'Create Client'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
