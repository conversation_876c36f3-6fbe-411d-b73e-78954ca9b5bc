import { useForm, useFieldArray } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet'
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Checkbox } from '@/components/ui/checkbox'
import { Separator } from '@/components/ui/separator'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from '@/hooks/use-toast'
import { workTimePeriodFormSchema, type WorkTimePeriodForm } from '../data/schema'
import { useTimesheet } from '../context/timesheet-context'
import { createWorkTimePeriod, updateWorkTimePeriod, getConsultantInfo, getClients } from '@/services/timesheet'
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query'
import { IconLoader2, IconPlus, IconTrash } from '@tabler/icons-react'
import { useEffect } from 'react'

interface Props {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function WorkTimePeriodDrawer({ open, onOpenChange }: Props) {
  const { currentPeriod, clients, setClients } = useTimesheet()
  const queryClient = useQueryClient()
  const isEditing = !!currentPeriod

  const form = useForm<WorkTimePeriodForm>({
    resolver: zodResolver(workTimePeriodFormSchema),
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(),
      remoteWorkPercentage: 0,
      notes: '',
      activities: [
        {
          activityName: '',
          hours: '',
          description: '',
          isBillable: true,
          clientId: undefined,
        },
      ],
    },
  })

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: 'activities',
  })

  // Load clients
  const { data: clientsData } = useQuery({
    queryKey: ['clients'],
    queryFn: getClients,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  useEffect(() => {
    if (clientsData) {
      setClients(clientsData)
    }
  }, [clientsData, setClients])

  // Set form values when editing
  useEffect(() => {
    if (currentPeriod && isEditing) {
      form.reset({
        startDate: new Date(currentPeriod.startDate),
        endDate: new Date(currentPeriod.endDate),
        remoteWorkPercentage: currentPeriod.remoteWorkPercentage,
        notes: currentPeriod.notes || '',
        activities: currentPeriod.activities.map(activity => ({
          activityName: activity.activityName,
          hours: activity.hours.toString(),
          description: activity.description || '',
          isBillable: activity.isBillable,
          clientId: activity.client?.id,
        })),
      })
    }
  }, [currentPeriod, isEditing, form])

  const createMutation = useMutation({
    mutationFn: createWorkTimePeriod,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Work time period created successfully',
      })
      queryClient.invalidateQueries({ queryKey: ['workTimePeriods'] })
      onOpenChange(false)
      form.reset()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create work time period',
        variant: 'destructive',
      })
    },
  })

  const updateMutation = useMutation({
    mutationFn: updateWorkTimePeriod,
    onSuccess: () => {
      toast({
        title: 'Success',
        description: 'Work time period updated successfully',
      })
      queryClient.invalidateQueries({ queryKey: ['workTimePeriods'] })
      onOpenChange(false)
      form.reset()
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update work time period',
        variant: 'destructive',
      })
    },
  })

  const onSubmit = (data: WorkTimePeriodForm) => {
    const consultantInfo = getConsultantInfo()
    if (!consultantInfo) {
      toast({
        title: 'Error',
        description: 'Consultant information not found',
        variant: 'destructive',
      })
      return
    }

    const totalHours = data.activities.reduce((sum, activity) => sum + parseFloat(activity.hours), 0)

    const periodData = {
      consultantId: parseInt(consultantInfo.id),
      startDate: data.startDate.toISOString().split('T')[0],
      endDate: data.endDate.toISOString().split('T')[0],
      totalHours,
      remoteWorkPercentage: data.remoteWorkPercentage,
      notes: data.notes,
      activities: data.activities.map(activity => ({
        activityName: activity.activityName,
        hours: parseFloat(activity.hours),
        description: activity.description,
        isBillable: activity.isBillable,
        clientId: activity.clientId,
      })),
    }

    if (isEditing && currentPeriod) {
      updateMutation.mutate({
        id: currentPeriod.id,
        ...periodData,
      })
    } else {
      createMutation.mutate(periodData)
    }
  }

  const addActivity = () => {
    append({
      activityName: '',
      hours: '',
      description: '',
      isBillable: true,
      clientId: undefined,
    })
  }

  const removeActivity = (index: number) => {
    if (fields.length > 1) {
      remove(index)
    }
  }

  const isLoading = createMutation.isPending || updateMutation.isPending

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className='w-[600px] sm:w-[700px] overflow-y-auto'>
        <SheetHeader>
          <SheetTitle>
            {isEditing ? 'Edit Work Time Period' : 'Add Work Time Period'}
          </SheetTitle>
          <SheetDescription>
            {isEditing
              ? 'Update the work time period details below.'
              : 'Fill in the details to add a new work time period with activities.'}
          </SheetDescription>
        </SheetHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6 mt-6'>
            {/* Period Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Period Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name='startDate'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Start Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            value={field.value.toISOString().split('T')[0]}
                            onChange={(e) => field.onChange(new Date(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name='endDate'
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>End Date</FormLabel>
                        <FormControl>
                          <Input
                            type="date"
                            value={field.value.toISOString().split('T')[0]}
                            onChange={(e) => field.onChange(new Date(e.target.value))}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name='remoteWorkPercentage'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Remote Work Percentage: {field.value}%</FormLabel>
                      <FormControl>
                        <Slider
                          value={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                          max={100}
                          step={5}
                          className="w-full"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name='notes'
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Notes (Optional)</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder='Add any notes about this period...'
                          className='resize-none'
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>

            {/* Activities */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Activities</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addActivity}>
                  <IconPlus className="h-4 w-4 mr-2" />
                  Add Activity
                </Button>
              </CardHeader>
              <CardContent className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="space-y-4 p-4 border rounded-lg">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">Activity {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeActivity(index)}
                        >
                          <IconTrash className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`activities.${index}.activityName`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Activity Name</FormLabel>
                            <FormControl>
                              <Input placeholder='e.g., Development, Meeting, Analysis' {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`activities.${index}.hours`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Hours</FormLabel>
                            <FormControl>
                              <Input
                                type='number'
                                step='0.5'
                                min='0'
                                max='24'
                                placeholder='8.0'
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <FormField
                        control={form.control}
                        name={`activities.${index}.clientId`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Client (Optional)</FormLabel>
                            <Select onValueChange={(value) => field.onChange(value ? parseInt(value) : undefined)} value={field.value?.toString()}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder='Select client' />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                {clients.map((client) => (
                                  <SelectItem key={client.id} value={client.id.toString()}>
                                    {client.name} ({client.code})
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name={`activities.${index}.isBillable`}
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0 pt-8">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>Billable</FormLabel>
                            </div>
                          </FormItem>
                        )}
                      />
                    </div>

                    <FormField
                      control={form.control}
                      name={`activities.${index}.description`}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Description (Optional)</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder='Describe the work performed...'
                              className='resize-none'
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>

            <Separator />

            <div className='flex justify-end space-x-2 pt-4'>
              <Button
                type='button'
                variant='outline'
                onClick={() => onOpenChange(false)}
                disabled={isLoading}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={isLoading}>
                {isLoading && (
                  <IconLoader2 className='mr-2 h-4 w-4 animate-spin' />
                )}
                {isEditing ? 'Update' : 'Create'} Period
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  )
}
