import { Header } from '@/components/layout/header'
import { Main } from '@/components/layout/main'
import { ProfileDropdown } from '@/components/profile-dropdown'
import { Search } from '@/components/search'
import { ThemeSwitch } from '@/components/theme-switch'
import { workTimePeriodColumns } from './components/work-time-period-columns'
import { DataTable } from './components/data-table'
import { WorkTimePeriodDialogs } from './components/work-time-period-dialogs'
import { WorkTimePeriodPrimaryButtons } from './components/work-time-period-primary-buttons'
import TimesheetProvider from './context/timesheet-context'
import { useQuery } from '@tanstack/react-query'
import { getConsultantWorkTimePeriods, getConsultantInfo } from '@/services/timesheet'
import { Skeleton } from '@/components/ui/skeleton'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { IconAlertCircle } from '@tabler/icons-react'

function TimesheetContent() {
  const consultantInfo = getConsultantInfo()

  const { data: workTimePeriods, isLoading, error } = useQuery({
    queryKey: ['workTimePeriods', consultantInfo?.id],
    queryFn: () => {
      if (!consultantInfo) throw new Error('Consultant information not found')
      return getConsultantWorkTimePeriods(parseInt(consultantInfo.id))
    },
    enabled: !!consultantInfo,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  if (!consultantInfo) {
    return (
      <Main>
        <Alert variant="destructive">
          <IconAlertCircle className="h-4 w-4" />
          <AlertDescription>
            Consultant information not found. Please log in again.
          </AlertDescription>
        </Alert>
      </Main>
    )
  }

  return (
    <Main>
      <div className='mb-2 flex flex-wrap items-center justify-between gap-x-4 space-y-2'>
        <div>
          <h2 className='text-2xl font-bold tracking-tight'>Work Time Tracking</h2>
          <p className='text-muted-foreground'>
            Manage your work time periods and activities efficiently.
          </p>
        </div>
        <WorkTimePeriodPrimaryButtons />
      </div>

      <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-x-12 lg:space-y-0'>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <IconAlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load work time periods. Please try again.
            </AlertDescription>
          </Alert>
        ) : (
          <DataTable data={workTimePeriods || []} columns={workTimePeriodColumns} />
        )}
      </div>
    </Main>
  )
}

export default function Timesheet() {
  return (
    <TimesheetProvider>
      <Header fixed>
        <Search />
        <div className='ml-auto flex items-center space-x-4'>
          <ThemeSwitch />
          <ProfileDropdown />
        </div>
      </Header>

      <TimesheetContent />
      <WorkTimePeriodDialogs />
    </TimesheetProvider>
  )
}
